import React, { useState, useEffect } from 'react';
import { Select, Button, Row, Col, message, Spin } from 'antd';
import moment from 'moment';
import http_utils from '../../util/http_utils';

const { Option } = Select;

const MacroBookingComponent = ({ selectedPrvdr, onWeekSelected, onCancel }) => {
    const [weekOptions, setWeekOptions] = useState([]);
    const [selectedWeek, setSelectedWeek] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        generateWeekOptions();
    }, []);

    const generateWeekOptions = () => {
        const weeks = [];
        const currentDate = moment();
        
        // Generate next 8 weeks starting from current date
        for (let i = 0; i < 8; i++) {
            const weekStart = currentDate.clone().add(i, 'weeks').startOf('week');
            const weekEnd = weekStart.clone().endOf('week');
            
            const weekLabel = `${weekStart.format('DD MMM')} - ${weekEnd.format('DD MMM YYYY')}`;
            const weekValue = {
                start: weekStart.format('YYYY-MM-DD'),
                end: weekEnd.format('YYYY-MM-DD'),
                label: weekLabel,
                weekNumber: i + 1
            };
            
            weeks.push(weekValue);
        }
        
        setWeekOptions(weeks);
    };

    const handleWeekSelection = (weekIndex) => {
        const selectedWeekData = weekOptions[weekIndex];
        setSelectedWeek(selectedWeekData);
    };

    const handleConfirmSelection = () => {
        if (!selectedWeek) {
            message.error('Please select a week');
            return;
        }

        // Prepare week data for bulk booking
        const weekData = {
            selectedWeek: selectedWeek,
            selectedPrvdr: selectedPrvdr,
            bookingMode: 'select_fulfillment_day'
        };

        onWeekSelected(weekData);
    };

    const fetchCapacityForWeek = async (weekData) => {
        setLoading(true);
        setError(null);
        
        try {
            const params = {
                org_id: selectedPrvdr,
                start_date: weekData.start,
                end_date: weekData.end,
            };

            const onComplete = (resp) => {
                console.log('Week capacity response:', resp);
                setLoading(false);
                // You can add capacity validation here if needed
            };

            const onError = (error) => {
                console.error('Error fetching week capacity:', error);
                setError(http_utils.decodeErrorToMessage(error));
                setLoading(false);
            };

            http_utils.performGetCall(
                '/booking/capacity',
                params,
                onComplete,
                onError
            );
        } catch (err) {
            setError('Failed to fetch capacity data');
            setLoading(false);
        }
    };

    return (
        <div style={{ padding: '20px' }}>
            <Row gutter={16}>
                <Col span={24}>
                    <div style={{ marginBottom: 20 }}>
                        <h3>Select Week for Fulfillment</h3>
                        <p>Choose the week when you want the service requests to be fulfilled:</p>
                    </div>
                </Col>
            </Row>

            <Row gutter={16}>
                <Col span={24}>
                    <div style={{ marginBottom: 20 }}>
                        <label>Available Weeks:</label>
                        <Select
                            style={{ width: '100%', marginTop: 8 }}
                            placeholder="Select a week"
                            onChange={handleWeekSelection}
                            value={selectedWeek ? weekOptions.findIndex(w => w.start === selectedWeek.start) : undefined}
                        >
                            {weekOptions.map((week, index) => (
                                <Option key={index} value={index}>
                                    Week {week.weekNumber}: {week.label}
                                </Option>
                            ))}
                        </Select>
                    </div>
                </Col>
            </Row>

            {selectedWeek && (
                <Row gutter={16}>
                    <Col span={24}>
                        <div style={{ 
                            marginBottom: 20, 
                            padding: 15, 
                            backgroundColor: '#f6f6f6', 
                            borderRadius: 6 
                        }}>
                            <h4>Selected Week Details:</h4>
                            <p><strong>Week:</strong> {selectedWeek.label}</p>
                            <p><strong>Start Date:</strong> {moment(selectedWeek.start).format('dddd, MMMM Do YYYY')}</p>
                            <p><strong>End Date:</strong> {moment(selectedWeek.end).format('dddd, MMMM Do YYYY')}</p>
                        </div>
                    </Col>
                </Row>
            )}

            {loading && (
                <Row gutter={16}>
                    <Col span={24} style={{ textAlign: 'center' }}>
                        <Spin size="large" />
                        <p>Checking capacity for selected week...</p>
                    </Col>
                </Row>
            )}

            {error && (
                <Row gutter={16}>
                    <Col span={24}>
                        <div style={{ color: 'red', marginBottom: 20 }}>
                            Error: {error}
                        </div>
                    </Col>
                </Row>
            )}

            <Row gutter={16}>
                <Col span={24} style={{ textAlign: 'right' }}>
                    <Button 
                        style={{ marginRight: 8 }} 
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button 
                        type="primary" 
                        onClick={handleConfirmSelection}
                        disabled={!selectedWeek || loading}
                    >
                        Proceed with Selected Week
                    </Button>
                </Col>
            </Row>
        </div>
    );
};

export default MacroBookingComponent;
