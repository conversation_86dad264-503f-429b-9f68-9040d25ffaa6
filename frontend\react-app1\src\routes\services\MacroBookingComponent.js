import React, { useState, useEffect } from 'react';
import { Select, Button, Row, Col, message, Spin } from 'antd';
import moment from 'moment';
import http_utils from '../../util/http_utils';
import BulkUploader from '../../components/wify-utils/BulkUploader';

const { Option } = Select;

const MacroBookingComponent = ({
    selectedPrvdr,
    onWeekSelected,
    onDataModified,
    onCancel,
    submitUrl,
    dataProto,
    orgSettingsData,
}) => {
    const [dayOptions, setDayOptions] = useState([]);
    const [selectedDay, setSelectedDay] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [showBulkUploader, setShowBulkUploader] = useState(false);

    useEffect(() => {
        generateDayOptions();
    }, []);

    const generateDayOptions = () => {
        const days = [];
        const currentDate = moment();

        // Generate 7 days starting from today (e.g., Wed, Jul 12)
        for (let i = 0; i < 7; i++) {
            const day = currentDate.clone().add(i, 'days'); // Get each day from today onward
            //i want label like this wednesday, Jul 12
            const dayLabel = `${day.format('dddd, MMM D')}`;
            //const dayLabel = `${day.format('ddd, MMM D')}`; // Format as "Wed, Jul 12"
            const dayValue = {
                date: day.format('YYYY-MM-DD'),
                label: dayLabel,
            };

            days.push(dayValue);
        }

        setDayOptions(days); // Set the day options in the state
    };

    const handleDaySelection = (dayIndex) => {
        const selectedDayData = dayOptions[dayIndex];
        setSelectedDay(selectedDayData);
        setShowBulkUploader(true);

        // Call the parent callback with the selected day data
        if (onWeekSelected) {
            onWeekSelected(selectedDayData); // Pass the selected day data to the parent
        }
    };

    const getEnhancedDataProto = () => {
        console.log('dataProto', dataProto);
        // Add booking data to the proto if available
        const enhancedProto = [...dataProto];

        enhancedProto.bookingMode = 'select_fulfillment_day';
        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }
        if (selectedDay) {
            enhancedProto.selectedDay = selectedDay; // Add selected day data
        }

        console.log('dataProtoenhancedProto', enhancedProto);

        return enhancedProto;
    };

    const fetchCapacityForDay = async (dayData) => {
        setLoading(true);
        setError(null);

        try {
            const params = {
                org_id: selectedPrvdr,
                start_date: dayData.date,
                end_date: dayData.date, // Single day selection
            };

            const onComplete = (resp) => {
                console.log('Day capacity response:', resp);
                setLoading(false);
                // You can add capacity validation here if needed
            };

            const onError = (error) => {
                console.error('Error fetching day capacity:', error);
                setError(http_utils.decodeErrorToMessage(error));
                setLoading(false);
            };

            http_utils.performGetCall(
                '/booking/capacity',
                params,
                onComplete,
                onError
            );
        } catch (err) {
            setError('Failed to fetch capacity data');
            setLoading(false);
        }
    };

    return (
        <div>
            <Row gutter={16}>
                <Col span={24}>
                    <div style={{ marginBottom: 20 }}>
                        <label>Select Day:</label>
                        <Select
                            style={{ width: '100%', marginTop: 8 }}
                            placeholder="Select a day"
                            onChange={handleDaySelection}
                            value={
                                selectedDay
                                    ? dayOptions.findIndex(
                                          (d) => d.date === selectedDay.date
                                      )
                                    : undefined
                            }
                        >
                            {dayOptions.map((day, index) => (
                                <Option key={index} value={index}>
                                    {day.label}
                                </Option>
                            ))}
                        </Select>
                    </div>
                </Col>
            </Row>

            {loading && (
                <Row gutter={16}>
                    <Col span={24} style={{ textAlign: 'center' }}>
                        <Spin size="large" />
                        <p>Checking capacity for selected day...</p>
                    </Col>
                </Row>
            )}

            {error && (
                <Row gutter={16}>
                    <Col span={24}>
                        <div style={{ color: 'red', marginBottom: 20 }}>
                            Error: {error}
                        </div>
                    </Col>
                </Row>
            )}

            {/* Render Bulk Uploader inside the same modal */}
            {showBulkUploader && (
                <Row gutter={16}>
                    <Col span={24}>
                        <div style={{ marginTop: 20 }}>
                            <BulkUploader
                                onDataModified={(entry_ids) => {
                                    setShowBulkUploader(false);
                                    if (onDataModified) {
                                        onDataModified(entry_ids);
                                    }
                                }}
                                submitUrl={submitUrl}
                                dataProto={getEnhancedDataProto()}
                                orgSettingsData={orgSettingsData}
                            />
                        </div>
                    </Col>
                </Row>
            )}
        </div>
    );
};


export default MacroBookingComponent;
