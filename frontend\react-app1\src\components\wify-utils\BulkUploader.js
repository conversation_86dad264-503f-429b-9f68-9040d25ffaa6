import {
    <PERSON><PERSON>,
    Button,
    Col,
    Input,
    List,
    message,
    Modal,
    Progress,
    Row,
    Spin,
    Table,
    Tag,
    Typography,
} from 'antd';
import FormBuilder from 'antd-form-builder';
import { OutTable, ExcelRenderer } from 'react-excel-renderer';
import React, { Component, useEffect, useState } from 'react';
import {
    convertDateFieldsToMoments,
    decodeAntdFormErrorsToString,
    ExcelDateToJSDate,
} from '../../util/helpers';
import moment from 'moment';
import { Form } from 'antd';
import { defaultStyles, FileIcon } from 'react-file-icon';
import http_utils from '../../util/http_utils';
import ReactExport from 'react-export-excel';
import { DownloadOutlined } from '@ant-design/icons';
import LocalStorageManager from '../../util/LocalStorageManager';
import ConfigHelpers from '../../util/ConfigHelpers';
import ruleValidator from './AntdRuleValidator';

const { Paragraph } = Typography;

const ExcelFile = ReactExport.ExcelFile;
const ExcelSheet = ReactExport.ExcelFile.ExcelSheet;
const ExcelColumn = ReactExport.ExcelFile.ExcelColumn;
const pageSize = 10;
// const defaultChunkSize = 1000;
const defaultMaxRows = 1000;
const dataProtoFrBulkUpload = [
    {
        label: 'Name',
        required: true,
        key: 'user_name',
    },
    {
        key: 'user_number',
        label: 'Mobile(+91)',
        required: true,
        rules: [
            {
                pattern: new RegExp('^[0-9]*$'),
                message: 'Incorrect number',
            },
            { min: 10 },
            { max: 10 },
        ],
    },
    {
        label: 'Gender',
        required: true,
        key: 'user_gender',
        widget: 'select',
        options: [
            {
                label: 'Male',
                value: 'male',
            },
            {
                label: 'Female',
                value: 'female',
            },
        ],
    },
    {
        label: 'Weight',
        required: false,
        key: 'user_weight',
        widget: 'number',
    },
    {
        label: 'DOB',
        required: false,
        key: 'user_dob',
        widget: 'date-picker',
    },
];

const SingleRowFormFrBuilder = (props) => {
    // const [form] = Form.useForm();
    const {
        meta,
        initialValues,
        onValidationError,
        onReadyToUpload,
        renderFullForm,
    } = props;
    const [error, setError] = useState(undefined);
    const [checked, setChecked] = useState(false);
    // useEffect(() => {
    //     if (!checked) {
    //         // console.log('BulkAssign Checked row',initialValues)
    //         setChecked(true);
    //         setTimeout(async () => {
    //             let formData = form.getFieldsValue();
    //             form.validateFields()
    //                 .then((values) => {
    //                     setError('success');
    //                     onReadyToUpload(formData);
    //                 })
    //                 .catch((err) => {
    //                     // console.log('err',err)
    //                     if (err.errorFields?.length > 0) {
    //                         setError(decodeAntdFormErrorsToString(err));
    //                         // setError(JSON.stringify(err.errors))
    //                         onValidationError(err);
    //                     } else {
    //                         onReadyToUpload(formData);
    //                     }
    //                 });
    //         }, 10);
    //     }
    // });
    if (!checked) {
        setChecked(true);
        setTimeout(async () => {
            const validate = await ruleValidator(
                meta.fields,
                initialValues,
                props.isBulkAssignComp
            );
            if (validate.length > 0) {
                const errorString = validate?.[0]?.errors.join(',');
                setError(errorString || 'Error');
                onValidationError(validate);
            } else {
                setError('success');
                onReadyToUpload(initialValues);
            }
        }, 10);
    }

    return !renderFullForm && error ? (
        error == 'success' ? (
            <Tag color="success">Ready</Tag>
        ) : (
            // <Tag color="error">{error}</Tag>
            <span className="gx-text-red">{error}</span>
        )
    ) : (
        // <>
        //     <Spin></Spin>
        //     <Form
        //         layout="vertical"
        //         form={form}
        //         className="gx-d-none"
        //         initialValues={initialValues}
        //     >
        //         <FormBuilder form={form} meta={meta} />
        //     </Form>
        // </>
        <></>
    );
};

class BulkUploader extends Component {
    initState = {
        cols: [],
        rows: [],
        bulkUploadInProgress: false,
        bulkUploadProgress: 0,
        clearFileInput: false,
        uploadComplete: false,
        errorInfile: false,
        erroricRows: 0,
        majorErrors: [],
        readyToUploadRows: [],
        readyToUploadRowKeys: [],
        uploadResp: '',
        fileCheckingInProg: false,
        currentPage: 1,
        fileName: '',
        isExcelUploaded: false,
        multipleFieldDataModals: {},
    };

    state = this.initState;

    showSelectFieldOptionsModal = (key) => {
        let multipleFieldDataModals = this.state.multipleFieldDataModals;
        multipleFieldDataModals[key] = true;
        this.setState({ multipleFieldDataModals });
    };
    hideSelectFieldOptionsModal = (key) => {
        let multipleFieldDataModals = this.state.multipleFieldDataModals;
        multipleFieldDataModals[key] = false;
        this.setState({ multipleFieldDataModals });
    };

    initFileCheckVars(resetMajorErroricRowsKeys = true) {
        this.readyToUploadRowKeys = {};
        this.readyToUploadRows = [];
        this.erroricRows = 0;
        this.erroricRowsKeys = {};
        if (resetMajorErroricRowsKeys) {
            this.majorErroricRowsKeys = {};
        }
    }

    constructor(props) {
        super(props);
        // console.log('Rxd props', this.props);
        this.initFileCheckVars();
    }

    getMeta() {
        let finalFields = [];
        let dataProto = this.getDataProto();
        dataProto.map((singleFieldMeta) => {
            if (singleFieldMeta.label) {
                singleFieldMeta.colSpan = 2;
                singleFieldMeta.renderView = () => <div></div>;
                finalFields.push(singleFieldMeta);
            }
        });
        const meta = {
            columns: this.state.rows[0].length,
            formItemLayout: null,
            fields: finalFields,
        };
        return meta;
    }

    getSingleColumFieldMeta(columnText) {
        let returnMeta = {};
        let dataProto = this.getDataProto();
        dataProto.map((singleFieldMeta) => {
            if (singleFieldMeta.label == columnText) {
                returnMeta = singleFieldMeta;
            }
        });
        return returnMeta;
    }

    getColumns(columsArray) {
        // this will be from an excel
        let firstRow = columsArray || this.state.rows[0];
        let columns = [];
        if (firstRow) {
            columns.push({
                title: 'Upload state',
                dataIndex: this.getUploadStateFieldIndex(),
                key: this.getUploadStateFieldIndex(),
                render: (text, record) => {
                    return (
                        <SingleRowFormFrBuilder
                            key={record.batch_data_row_key}
                            meta={this.getMeta()}
                            renderFullForm={this.props.renderFormsForRows}
                            initialValues={this.getFormDataForRow(record)}
                            onValidationError={(e) =>
                                this.handleFailedRow(record.batch_data_row_key)
                            }
                            onReadyToUpload={(formData) =>
                                this.handleReadyToUpload(
                                    formData,
                                    record.batch_data_row_key
                                )
                            }
                            isBulkAssignComp={this.props.isBulkAssignComp}
                        />
                    );
                },
            });
            !this.props.renderFormsForRows &&
                firstRow.map((singleCellValue, index) => {
                    let fieldMeta =
                        this.getSingleColumFieldMeta(singleCellValue);
                    let singleColumnData = {
                        title: singleCellValue,
                        dataIndex: index,
                        key: index,
                        className: fieldMeta.required
                            ? 'gx-bg-amber-light'
                            : '',
                    };
                    if (fieldMeta.widget == 'date-picker') {
                        singleColumnData.render = (text, record) => {
                            // this is a date field
                            if (text != '') {
                                let value = ExcelDateToJSDate(text);
                                if (value) {
                                    value = moment(value);
                                    value = value.format('MMM-DD-YYYY');
                                } else {
                                    value = moment(value);
                                }
                                return value;
                            }
                            return text;
                        };
                    }

                    columns.push(singleColumnData);
                });
        }
        return columns;
    }

    getUploadStateFieldIndex() {
        // return this.state?.rows[0]?.length;
        return 0;
    }

    getData(rowsInState = undefined) {
        let rows = rowsInState || this.state.rows;
        let finalRows = [];
        rows.map((singleRow, index) => {
            // first is header
            if (index > 0) {
                singleRow.batch_data_row_key = index;
                finalRows.push(singleRow);
            }
        });
        // console.log('rows',rows)
        return finalRows;
    }

    async handleFileChange(event) {
        let fileObj = event.target.files[0];
        try {
            await message.loading('Loading excel...');

            const excelData = await new Promise((resolve, reject) => {
                ExcelRenderer(fileObj, (err, resp) => {
                    if (err) {
                        reject('Unable to read the excel');
                    } else {
                        resolve(resp);
                    }
                });
            });

            const header = excelData.rows[0];
            const CHUNK_SIZE = 100;
            const totalRows = excelData.rows.length;
            let startRow = 1;

            // Process the rows in chunks
            while (startRow < totalRows) {
                const chunkRows = excelData.rows.slice(
                    startRow,
                    startRow + CHUNK_SIZE
                );
                const chunkWithHeader = [header, ...chunkRows];

                const processedRows = chunkWithHeader.filter((singleRow) => {
                    if (singleRow.length > 0) {
                        singleRow = singleRow.filter(
                            (cellVal) => cellVal && cellVal !== ''
                        );
                    }
                    return singleRow.length > 0;
                });
                this.doBasicValidationsAndRereshUI(
                    { rows: processedRows, cols: excelData.cols },
                    fileObj.name,
                    startRow
                );

                startRow += CHUNK_SIZE;
            }
            this.setState({ isExcelUploaded: true });
            message.success('Excel loaded successfully.');
        } catch (e) {
            message.error(e || 'Unable to read the excel');
            this.initFileCheckVars();
            this.setState(this.initState);
        }
    }

    doBasicValidationsAndRereshUI(resp, fileName, startRow) {
        let majorErrors = [];
        let dataProto = this.getDataProto();
        let maxRows = this.props.maxRows;
        let currentRowsLength = resp.rows.length - 1;
        if (maxRows && currentRowsLength > maxRows) {
            majorErrors.push(
                `Total rows in excel (${currentRowsLength}) exceeds maximum allowed ${maxRows} rows`
            );
        }
        let acceptedColumns = dataProto.map((fieldMeta) => fieldMeta.label);
        let requiredFields = dataProto?.filter(
            (fieldMeta) => fieldMeta.required
        );
        // console.log('Required fields',requiredFields);
        let columns = resp.rows[0];
        let missingReqFields = requiredFields.filter(
            (fieldMeta) => !columns.includes(fieldMeta.label)
        );
        if (missingReqFields.length > 0) {
            majorErrors.push(
                'Missing column - ' +
                    missingReqFields
                        .map((fieldMeta) => fieldMeta.label)
                        .join(',')
            );
        }
        // console.log('majorErrors',majorErrors);
        let unRecognisedFields = columns.filter(
            (columnTitle) => !acceptedColumns.includes(columnTitle)
        );
        // console.log('unRecognisedFields',unRecognisedFields);
        if (unRecognisedFields.length > 0) {
            majorErrors.push(
                ' Unrecognised column - ' + unRecognisedFields.join(',')
            );
        }
        this.validateDateCols(resp);
        // let erroricDateCols = this.validateDateCols(resp);
        // if(erroricDateCols.length > 0){
        //     majorErrors.push('Invalid date format used for - ' + erroricDateCols.join(',') + ' please use YYYY-MM-DD ')
        // }
        // this.validatePincodeCols(resp);
        // let erroricPincodeCols = this.validatePincodeCols(resp);
        // if(erroricPincodeCols.length > 0){
        //     majorErrors.push(' Invalid used for - ' + erroricPincodeCols.join(','))
        // }
        let uploaded_file_name = localStorage.getItem(
            'RECENT_UPLOADED_SRVC_REQ_EXCEL'
        );
        if (fileName == uploaded_file_name) {
            majorErrors.push(
                'Duplicate excel !! This excel was already uploaded successfully'
            );
        }
        // init state and then set new values to #bug fix
        if (Object.keys(this.majorErroricRowsKeys).length > 0) {
            this.initFileCheckVars(false);
        } else {
            this.initFileCheckVars();
        }
        if (startRow != 1) {
            resp.rows = resp.rows.slice(1);
        }
        // reject();
        // this.setState(this.initState, () => {
        this.setState({
            cols: resp.cols,
            rows: [...this.state.rows, ...resp.rows],
            errorInfile:
                majorErrors.length > 0 ||
                Object.keys(this.majorErroricRowsKeys).length > 0,
            majorErrors: majorErrors,
            readyToUploadRows: [],
            fileCheckingInProg: true,
            bulkUploadProgress: 1,
            fileName: fileName,
        });
    }

    validatePincodeCols(resp) {
        let erroricColumns = [];
        let columns = resp.rows[0];
        let onlyRows = [...resp.rows];
        delete onlyRows[0];
        onlyRows.map((singleRow, rowNo) => {
            singleRow.map((singleCellValue, index) => {
                let fieldName = columns[index];
                let fieldMeta = this.getSingleColumFieldMeta(fieldName);
                let value = singleCellValue.toString().length;
                if (fieldMeta.key == 'cust_pincode') {
                    // let org_pincode_length = ConfigHelpers.getOrgPincodeLength();
                    if (
                        singleCellValue != '' &&
                        value !=
                            (this.props.orgSettingsData
                                ?.selected_country_pincode_length ?? 6)
                    ) {
                        erroricColumns.push(fieldMeta.label);
                        if (!this.majorErroricRowsKeys[rowNo]) {
                            this.majorErroricRowsKeys[rowNo] = true;
                        }
                    }
                }
            });
        });
        return erroricColumns;
    }

    validateDateCols(resp) {
        let erroricColumns = [];
        let columns = resp.rows[0];
        let onlyRows = [...resp.rows];
        delete onlyRows[0];
        onlyRows.map((singleRow, rowNo) => {
            singleRow.map((singleCellValue, index) => {
                let fieldName = columns[index];
                let fieldMeta = this.getSingleColumFieldMeta(fieldName);
                if (fieldMeta.widget == 'date-picker') {
                    if (singleCellValue != '') {
                        let value = ExcelDateToJSDate(singleCellValue);
                        if (value) {
                            // value = moment(value);
                            // value = value.format('MMM-DD-YYYY');
                        } else {
                            //
                            erroricColumns.push(fieldMeta.label);
                            if (!this.majorErroricRowsKeys[rowNo]) {
                                this.majorErroricRowsKeys[rowNo] = true;
                            }
                        }
                    }
                }
            });
        });
        return erroricColumns;
    }

    getFormDataForRow(rowData) {
        let columns = this.state.rows[0];
        let formDataProtoFrRow = {};
        columns.map((columnTitle, index) => {
            let singleFieldMeta = this.getSingleColumFieldMeta(columnTitle);
            let value = rowData[index];
            if (singleFieldMeta.widget == 'date-picker') {
                // console.log('value',value);
                if (value) {
                    // // this is a date field
                    let valueConverted = ExcelDateToJSDate(value);
                    if (valueConverted) {
                        value = moment(valueConverted);
                        value = value.format('YYYY-MM-DD');
                    } else {
                        value = moment(value);
                    }
                } else {
                    value = undefined;
                }
            } else if (
                singleFieldMeta.widget != 'select' &&
                singleFieldMeta.widget != 'number' &&
                value
            ) {
                value = '' + value;
            } else if (
                this.props.update &&
                !value &&
                singleFieldMeta.widget != 'select'
            ) {
                value = '';
            } else if (
                this.props.bulkUpdateAuthority &&
                !value &&
                singleFieldMeta.widget == 'select'
            ) {
                value = '';
            }
            if (
                (value || !this.props.bulkUpdateAuthority) &&
                singleFieldMeta.options
            ) {
                value = '' + value;
                // this has options the value should not be out of the set
                if (
                    singleFieldMeta.widgetProps?.mode == 'multiple' &&
                    value?.split(',')?.length >= 1
                ) {
                    // the entry has multiple values
                    let finalValue = [];
                    value.split(',').map((singleValue) => {
                        let matchingEntry = singleFieldMeta.options.filter(
                            (singleOption) =>
                                singleOption.label == singleValue?.trim()
                        );
                        if (matchingEntry.length > 0) {
                            finalValue.push(matchingEntry[0].value);
                        }
                    });
                    if (finalValue.length == 0) {
                        value = undefined;
                    } else {
                        value = finalValue;
                    }
                } else {
                    let matchingEntry;
                    if (this.props.bulkUpdateAuthority) {
                        matchingEntry = singleFieldMeta.options.filter(
                            (singleOption) =>
                                singleOption.label?.trim() == value?.trim()
                        );
                    } else {
                        matchingEntry = singleFieldMeta.options.filter(
                            (singleOption) =>
                                singleOption.label == value?.trim()
                        );
                    }
                    if (matchingEntry.length == 0) {
                        value = undefined;
                    } else {
                        value = matchingEntry[0].value;
                    }
                }
            }

            formDataProtoFrRow[singleFieldMeta.key] = value;
        });
        formDataProtoFrRow = convertDateFieldsToMoments(
            formDataProtoFrRow,
            this.getDataProto()
        );
        return formDataProtoFrRow;
    }

    isReadyToUpload() {
        if (this.getData()?.length > 0) {
            return true;
        }
        return false;
    }

    getDataProto() {
        let { dataProto, demoMode, debugMode, multipleFieldDataModals } =
            this.props;
        if (demoMode && !dataProto) {
            return dataProtoFrBulkUpload;
        }

         console.log('dataProto==>', dataProto);
        return dataProto;
    }

    onStartBulkCreation(event) {
        // if(this.props.demoMode){
        //     this.setState({
        //         bulkUploadInProgress:true,
        //         bulkUploadProgress: 0
        //     },this.dummyProgUpload(0))
        //     return;
        // }

        // initiate bulk upload
        this.setState({
            bulkUploadInProgress: true,
            bulkUploadProgress: 50,
        });

        let params = {};
        params['batch_data'] = this.getPostData();
        const onComplete = (resp) => {
            // console.log('Bulk resp',resp);
            this.setState(
                {
                    bulkUploadInProgress: false,
                    bulkUploadProgress: 100,
                    uploadComplete: true,
                    uploadResp: `Uploaded successfully`,
                },
                this.tellParent(resp.data.entry_ids)
            );
            // this.updateClosureToParent();
            LocalStorageManager.setData(
                'RECENT_UPLOADED_SRVC_REQ_EXCEL',
                this.state.fileName
            );
        };
        const onError = (error) => {
            // compare statuses here
            let errorDecoded = http_utils.decodeErrorToMessage(error);
            if (typeof errorDecoded == 'string') {
                errorDecoded = JSON.parse(errorDecoded).hint;
            }
            this.setState({
                bulkUploadInProgress: false,
                bulkUploadProgress: 100,
                uploadComplete: true,
                uploadResp: errorDecoded,
            });
        };
        http_utils.performPostCall(
            this.props.submitUrl,
            params,
            onComplete,
            onError
        );

        // let chunkSize = this.props.uploadChunkSize || defaultChunkSize;
        // let readyToUploadRows = this.state.readyToUploadRows;
        // let chunksToUpload = _.chunk(readyToUploadRows,chunkSize);
        // console.log('chunks',chunksToUpload)
    }

    tellParent(entry_ids) {
        if (this.props.onDataModified) {
            this.props.onDataModified(entry_ids);
        }
    }

    dummyProgUpload(prog) {
        if (prog > 100) {
            this.setState({
                bulkUploadInProgress: false,
                bulkUploadProgress: 100,
                uploadComplete: true,
            });
        } else {
            prog = prog + 10;
            this.setState(
                {
                    bulkUploadInProgress: true,
                    bulkUploadProgress: prog,
                },
                () =>
                    setTimeout((prog) => this.dummyProgUpload(prog), 1000, prog)
            );
        }
    }

    handleClear() {
        this.initFileCheckVars();
        this.setState(
            {
                uploadComplete: false,
                clearFileInput: true,
            },
            () =>
                this.setState({
                    cols: [],
                    rows: [],
                    readyToUploadRows: [],
                    clearFileInput: false,
                })
        );
    }

    handleFailedRow(batch_data_row_key) {
        if (!this.erroricRowsKeys[batch_data_row_key]) {
            this.erroricRowsKeys[batch_data_row_key] = true;
            this.erroricRows = this.erroricRows + 1;
        }

        if (!this.state.errorInfile) {
            this.setState({
                errorInfile: true,
            });
        }
        this.checkIfAllRowsScanningIsDone();
    }

    handleReadyToUpload(readyToUploadRow, batch_data_row_key) {
        let readyToUploadRowKeys = this.readyToUploadRowKeys;
        // console.log('readyToUploadRowKeys',readyToUploadRowKeys);
        if (!readyToUploadRowKeys[batch_data_row_key]) {
            let readyToUploadRows = this.readyToUploadRows;
            readyToUploadRows.push(readyToUploadRow);
            readyToUploadRowKeys[batch_data_row_key] = true;
            this.readyToUploadRowKeys = readyToUploadRowKeys;
            this.readyToUploadRows = readyToUploadRows;
        }

        this.checkIfAllRowsScanningIsDone();
    }

    checkIfAllRowsScanningIsDone() {
        let numOfValidRows = this.readyToUploadRows.length;
        // console.log("numOfValidRows",numOfValidRows);
        let erroricRows = this.erroricRows;
        // console.log("erroricRows",erroricRows);
        let totalRowsScanned = numOfValidRows + erroricRows;
        let totalRows = this.getData().length;
        if (
            totalRowsScanned == totalRows &&
            this.state.readyToUploadRows.length == 0
        ) {
            // this means all processing is done\
            // console.log('All processing done');
            // lets update the state now
            this.setState({
                erroricRows: erroricRows,
                readyToUploadRowKeys: this.readyToUploadRowKeys,
                readyToUploadRows: this.readyToUploadRows,
                fileCheckingInProg: false,
                currentPage: this.state.isExcelUploaded
                    ? 1
                    : this.state.currentPage,
                bulkUploadProgress: 0,
                isExcelUploaded: false,
            });
        } else if (this.state.fileCheckingInProg) {
            // we will need to see if there is a need to page
            let numberOfPages = Math.ceil(totalRows / 10);
            let currentPage = this.state.currentPage;
            if (
                currentPage <= numberOfPages &&
                totalRowsScanned == currentPage * 10
            ) {
                this.setState({
                    currentPage: currentPage + 1,
                    bulkUploadProgress: Math.round(
                        (currentPage / numberOfPages) * 100
                    ),
                });
                // console.log('We need to change page now');
            }
        }
    }

    getPostData() {
        let postData = this.state.readyToUploadRows;

        // Add booking data if available from dataProto
        if (this.props.dataProto) {
            const {
                selectedPrvdr,
                bookingMode,
                selectedWeekData,
                selectedSlotData,
            } = this.props.dataProto;

            if (
                selectedPrvdr ||
                bookingMode ||
                selectedWeekData ||
                selectedSlotData
            ) {
                // Add booking information to each row
                postData = postData.map((row) => ({
                    ...row,
                    ...(selectedPrvdr && { bulk_booking_prvdr: selectedPrvdr }),
                    ...(bookingMode && { bulk_booking_mode: bookingMode }),
                    ...(selectedWeekData && {
                        bulk_booking_week_data: selectedWeekData,
                    }),
                    ...(selectedSlotData && {
                        bulk_booking_slot_data: selectedSlotData,
                    }),
                }));
            }
        }

        return postData;
    }

    getPageNoOnTheBaseOfRow(erroricRowsKey) {
        return Math.ceil(erroricRowsKey / pageSize);
    }
    getRowOnTheBaseOfPageSize(erroricRowsKey) {
        if (erroricRowsKey > pageSize) {
            return Math.ceil(erroricRowsKey % pageSize)
                ? Math.ceil(erroricRowsKey % pageSize)
                : pageSize;
        }
        return erroricRowsKey;
    }
    render() {
        let { demoMode, debugMode, maxRows, renderFormsForRows } = this.props;
        maxRows = maxRows || defaultMaxRows;
        const {
            bulkUploadInProgress,
            bulkUploadProgress,
            uploadComplete,
            clearFileInput,
            errorInfile,
            erroricRows,
            majorErrors,
            uploadResp,
            fileCheckingInProg,
        } = this.state;
        const clearInputFileProp = clearFileInput ? { value: '' } : {};
        const readyToUpload = this.isReadyToUpload();
        const dataProto = this.getDataProto();
        return (
            <div>
                {demoMode && (
                    <Alert
                        message="Running in demo mode"
                        type="warning"
                    ></Alert>
                )}
                {debugMode && (
                    <div>
                        <Input
                            placeholder="Output JSON will show here"
                            value={JSON.stringify(this.getPostData())}
                        />
                    </div>
                )}
                {!this.props.update && (
                    <div className="">
                        <h5>
                            Accepted columns
                            <small>( * are mandatory )</small>
                            <ExcelFile
                                filename={
                                    this.props.update
                                        ? 'Bulk_update_template'
                                        : 'Bulk_upload_template'
                                }
                                element={
                                    <Button
                                        icon={<DownloadOutlined />}
                                        className="gx-mb-0"
                                        type="link"
                                    >
                                        Template
                                    </Button>
                                }
                            >
                                <ExcelSheet data={[]} name="Sheet 1">
                                    {dataProto
                                        .filter((fieldMeta) => fieldMeta.label)
                                        .map((fieldMeta) => (
                                            <ExcelColumn
                                                label={fieldMeta.label}
                                                value={fieldMeta.label}
                                                style={{
                                                    fill: {
                                                        patternType: 'solid',
                                                        fgColor: {
                                                            rgb: 'FFFF0000',
                                                        },
                                                    },
                                                }}
                                            />
                                        ))}
                                </ExcelSheet>
                            </ExcelFile>
                        </h5>
                        <div
                            style={{
                                overflowX: 'auto',
                            }}
                        >
                            <table class="gx-w-100">
                                <tbody>
                                    <tr>
                                        {dataProto.map(
                                            (singleFieldMeta, index) =>
                                                singleFieldMeta.label && (
                                                    <td
                                                        key={index}
                                                        className={
                                                            singleFieldMeta.required
                                                                ? 'gx-bg-amber-light'
                                                                : ''
                                                        }
                                                        style={{
                                                            border: '2px solid #dddddd',
                                                            textAlign: 'center',
                                                            padding: '8px',
                                                            fontSize: '0.8rem',
                                                        }}
                                                    >
                                                        {singleFieldMeta.label}
                                                        {singleFieldMeta.options && (
                                                            <td className="gx-d-flex gx-justify-content-center gx-align-items-center">
                                                                <Button
                                                                    type="link"
                                                                    onClick={() =>
                                                                        this.showSelectFieldOptionsModal(
                                                                            singleFieldMeta.key
                                                                        )
                                                                    }
                                                                    size="small"
                                                                >
                                                                    View Data
                                                                </Button>
                                                                <Modal
                                                                    title={`${singleFieldMeta.label} list`}
                                                                    visible={
                                                                        this
                                                                            .state
                                                                            .multipleFieldDataModals[
                                                                            singleFieldMeta
                                                                                .key
                                                                        ]
                                                                    }
                                                                    footer={
                                                                        null
                                                                    }
                                                                    onCancel={() =>
                                                                        this.hideSelectFieldOptionsModal(
                                                                            singleFieldMeta.key
                                                                        )
                                                                    }
                                                                >
                                                                    <p>
                                                                        {/* Map over the `options` array from `singleFieldMeta` */}
                                                                        {singleFieldMeta.options.map(
                                                                            (
                                                                                option,
                                                                                index
                                                                            ) => (
                                                                                <Paragraph
                                                                                    className="gx-mb-1"
                                                                                    key={
                                                                                        index
                                                                                    }
                                                                                    copyable={{
                                                                                        text: `${option?.label}`, // Text to be copied
                                                                                    }}
                                                                                >
                                                                                    {/* Display the option number (1-based index) */}
                                                                                    {index +
                                                                                        1}

                                                                                    .{' '}
                                                                                    {/* Display the label of the current option */}
                                                                                    {
                                                                                        option?.label
                                                                                    }
                                                                                </Paragraph>
                                                                            )
                                                                        )}
                                                                    </p>
                                                                </Modal>
                                                            </td>
                                                        )}
                                                    </td>
                                                )
                                        )}
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <hr></hr>
                    </div>
                )}
                <div className="gx-mt-3">
                    <h3>Upload an excel</h3>
                    <div className="gx-d-flex">
                        <div style={{ width: '30px' }}>
                            <FileIcon
                                extension=".xlsx"
                                {...defaultStyles['xlsx']}
                            />
                        </div>
                        <input
                            type="file"
                            {...clearInputFileProp}
                            onClick={(e) =>
                                this.setState({ fileCheckingInProg: true })
                            }
                            disabled={bulkUploadInProgress || uploadComplete}
                            onChange={(e) => {
                                this.handleFileChange(e);
                            }}
                            style={{ padding: '10px' }}
                            data-testid="upload-excel-file"
                        />
                    </div>

                    {bulkUploadProgress > 0 && bulkUploadProgress < 100 && (
                        <>
                            <Spin></Spin>
                            <br></br>
                            Checking file..
                            <Progress
                                // type="circle"
                                className="gx-ml-2"
                                percent={bulkUploadProgress}
                            />
                        </>
                    )}
                    {maxRows > 0 && !this.props.update && (
                        <p>**Max {maxRows} rows supported at a time</p>
                    )}
                    {!this.props.update && (
                        <div>
                            <p>
                                **If a column has data condition and if data is
                                out of possible values then it will be treated
                                as empty{' '}
                            </p>
                            <p className="gx-text-orange">
                                **Date format should be YYYY-MM-DD{' '}
                            </p>
                        </div>
                    )}
                    {this.props.timeFormatMsg && !this.props.update && (
                        <p className="gx-text-red">
                            **Time format should be HH:MMAM/PM (Eg:
                            09:30AM){' '}
                        </p>
                    )}
                    {readyToUpload && (
                        <>
                            {majorErrors.length > 0 && (
                                <>
                                    {majorErrors.map((singleLine, index) => (
                                        <p className="gx-text-red" key={index}>
                                            {singleLine}
                                        </p>
                                    ))}
                                </>
                            )}
                            {Object.keys(this.majorErroricRowsKeys).length >
                                0 && (
                                <>
                                    {Object.keys(this.majorErroricRowsKeys).map(
                                        (majorErroricRowsKey) => (
                                            <p className="gx-text-red">
                                                error on page{' '}
                                                {this.getPageNoOnTheBaseOfRow(
                                                    majorErroricRowsKey
                                                )}{' '}
                                                in row{' '}
                                                {this.getRowOnTheBaseOfPageSize(
                                                    majorErroricRowsKey
                                                )}
                                            </p>
                                        )
                                    )}
                                </>
                            )}
                            {errorInfile && erroricRows > 0 && (
                                <>
                                    {Object.keys(this.erroricRowsKeys).map(
                                        (singleErroricRowsKey) => (
                                            <p className="gx-text-red">
                                                error on page{' '}
                                                {this.getPageNoOnTheBaseOfRow(
                                                    singleErroricRowsKey
                                                )}{' '}
                                                in row{' '}
                                                {this.getRowOnTheBaseOfPageSize(
                                                    singleErroricRowsKey
                                                )}
                                            </p>
                                        )
                                    )}
                                </>
                            )}
                            <Button
                                type="primary"
                                disabled={
                                    !readyToUpload ||
                                    bulkUploadInProgress ||
                                    uploadComplete ||
                                    errorInfile ||
                                    fileCheckingInProg
                                }
                                onClick={(e) => {
                                    this.onStartBulkCreation(e);
                                }}
                            >
                                {this.props.update
                                    ? 'Start bulk updation'
                                    : 'Start bulk creation'}
                            </Button>
                            {
                                // uploadComplete &&
                                <>
                                    <Button
                                        onClick={(e) => this.handleClear()}
                                        type="link"
                                    >
                                        Reset/Clear
                                    </Button>
                                    <br></br>
                                    {uploadComplete && (
                                        <Alert
                                            type="info"
                                            message={
                                                <div>
                                                    <h2>Bulk upload results</h2>
                                                    <br></br>
                                                    <p
                                                        style={
                                                            this.props
                                                                .errorHasLineBreaks
                                                                ? {
                                                                      whiteSpace:
                                                                          'pre-wrap',
                                                                  }
                                                                : {}
                                                        }
                                                    >
                                                        {uploadResp}
                                                    </p>
                                                </div>
                                            }
                                        />
                                    )}
                                </>
                            }
                            {bulkUploadInProgress && (
                                <>
                                    <Spin></Spin>
                                    <br></br>
                                    Upload in progress..
                                </>
                            )}

                            {renderFormsForRows ? (
                                <List
                                    itemLayout="itemLayout"
                                    size="large"
                                    bordered
                                    pagination={{
                                        onChange: (page) => {
                                            console.log(page);
                                            this.setState({
                                                currentPage: page,
                                            });
                                        },
                                        pageSize: 10,
                                        current: this.state.currentPage,
                                    }}
                                    dataSource={this.getData()}
                                    renderItem={(item) => (
                                        <List.Item
                                            key={item.batch_data_row_key}
                                        >
                                            <SingleRowFormFrBuilder
                                                meta={this.getMeta()}
                                                renderFullForm
                                                initialValues={this.getFormDataForRow(
                                                    item
                                                )}
                                                onValidationError={(e) =>
                                                    this.handleFailedRow(
                                                        item.batch_data_row_key
                                                    )
                                                }
                                                onReadyToUpload={(formData) =>
                                                    this.handleReadyToUpload(
                                                        formData
                                                    )
                                                }
                                            />
                                        </List.Item>
                                    )}
                                />
                            ) : (
                                <Table
                                    className="gx-table-responsive"
                                    bordered
                                    size="small"
                                    pagination={{
                                        onChange: (page) => {
                                            console.log(page);
                                            this.setState({
                                                currentPage: page,
                                            });
                                        },
                                        pageSize: 10,
                                        current: this.state.currentPage,
                                    }}
                                    columns={this.getColumns()}
                                    dataSource={this.getData()}
                                    // dataSource={[]}
                                />
                            )}
                        </>
                    )}
                </div>
            </div>
        );
    }
}

export default BulkUploader;
