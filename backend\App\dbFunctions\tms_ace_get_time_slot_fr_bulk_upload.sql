CREATE OR REPLACE FUNCTION public.tms_ace_get_time_slot_fr_bulk_upload(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;
    day_ date;

	slots json;
	org_timezone text;
    service_hub int;
    pincode_ text;
    vertical_id_ int;
    skill_id_ int;
    -- usr_capacity_details json;

    org_availability_slots_config json;
	current_date_ date;
    capacity_json json;

begin
	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = form_data->>'usr_id';
	ip_address_ = form_data->>'ip_address';  
	user_agent_ = form_data->>'user_agent';
    org_id_ = (form_data->>'org_id')::integer;
    day_ = (form_data->>'selectedDay')::date;
   
    vertical_id_ = form_data->>'vertical_id';
   
	org_timezone = tms_hlpr_get_org_timezone(org_id_);
    current_date_ = (now() at time zone 'utc');
 
    if org_timezone is null then
		status = false;
		message = 'org_timezone_missing';
		resp_data = jsonb_build_object(
		    'status', status,
		    'code', message
		);
		return resp_data;
    end if;

    select  org_settings.settings_data
      from  public.cl_tx_orgs_settings as org_settings
     where  org_settings.org_id = org_id_
       and  org_settings.settings_type = 'ACE_AVAILABILITY_SLOTS_CONFIG'
      into  org_availability_slots_config;

     if org_availability_slots_config is null then
        status = false;
        message = 'capacity_details_missing';
        resp_data = jsonb_build_object(
            'status', status,
            'code', message
        );
        return resp_data;
        --raise exception 'capacity_details_missing';
     end if;

     resp_data =  array_to_json(array(
                    select jsonb_build_object(
                        'db_id', slot_.db_id,
                        'start_time',slot_.start_time ,
                        'end_time', slot_.end_time,
                      
                      
                        'is_available', false
                    )
                      from cl_tx_vertical_time_slots as slot_                     
                     where slot_.org_id = org_id_
                       and slot_.vertical_id = vertical_id_
                       and slot_.is_active is true
                     
                       and (
                            (
                               (('2000-01-01'::date + slot_.start_time) at time zone 'UTC')
                               at time zone org_timezone
                            )::time
                        ) >= (now() at time zone 'utc')::time   
                        and (
                            (
                               (('2000-01-01'::date + slot_.end_time) at time zone 'UTC')
                               at time zone org_timezone
                            )::time
                        ) <= (now() at time zone 'utc')::time


    ));

    status = true;
    message = 'success';

	return json_build_object('status',status,'code',message,'data',resp_data);

end;
$function$
;
