CREATE OR REPLACE FUNCTION public.tms_hlpr_update_srvc_req_booking_data(
    srvc_req_id_ bigint,
    capacity_id_ bigint default null,
    booking_details_ jsonb default null
)
RETURNS json
LANGUAGE plpgsql
AS $function$
declare
    status boolean default false;
    message text default 'Internal error';
    affected_rows integer;
    timeline_resp json;
    timeline_message text;
begin
    -- Validate input
    if srvc_req_id_ is null then
        return json_build_object('status', false, 'message', 'Service request ID is required');
    end if;
    
    -- Update the service request with booking data
    update cl_tx_srvc_req 
    set 
        capacity_id = case 
            when capacity_id_ is not null then capacity_id_
            else capacity_id
        end,
        booking_details = case 
            when booking_details_ is not null then booking_details_
            else booking_details
        end,
        u_on = now() at time zone 'utc'
    where db_id = srvc_req_id_;
    
    get diagnostics affected_rows = ROW_COUNT;
    
    if affected_rows > 0 then
        status = true;
        message = 'Booking data updated successfully';
        
        -- Add timeline entry based on booking data
        if booking_details_ is not null then
            if booking_details_->>'booking_mode' = 'fulfillment_week' then
                timeline_message = 'Bulk booking: Week selected for fulfillment - ' || 
                    (booking_details_->'selected_week'->>'label');
            elsif booking_details_->>'booking_mode' = 'specific_slot' then
                timeline_message = 'Bulk booking: Specific slot selected - ' || 
                    (booking_details_->>'booking_date') || ' at ' || 
                    (booking_details_->>'slot_label');
            else
                timeline_message = 'Bulk booking data updated';
            end if;
            
            -- Add timeline entry (assuming we have access to srvc_type_id)
            -- We'll need to get the srvc_type_id from the service request
            declare
                srvc_type_id_ integer;
            begin
                select srvc_type_id into srvc_type_id_ 
                from cl_tx_srvc_req 
                where db_id = srvc_req_id_;
                
                if srvc_type_id_ is not null then
                    timeline_resp = tms_add_to_srvc_req_timeline(
                        srvc_type_id_, 
                        srvc_req_id_, 
                        'UPDATE', 
                        timeline_message, 
                        booking_details_::json
                    );
                end if;
            end;
        end if;
    else
        message = 'Service request not found or no changes made';
    end if;
    
    return json_build_object(
        'status', status,
        'message', message,
        'affected_rows', affected_rows
    );
    
exception when others then
    return json_build_object(
        'status', false,
        'message', 'Error updating booking data: ' || SQLERRM
    );
end;
$function$;
