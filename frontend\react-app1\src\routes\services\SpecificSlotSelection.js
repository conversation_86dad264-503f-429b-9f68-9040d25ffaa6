import React, { useState, useEffect } from 'react';
import { Select, Button, Row, Col, message, Spin, Card, Tag } from 'antd';
import moment from 'moment';
import http_utils from '../../util/http_utils';
import BulkUploader from '../../components/wify-utils/BulkUploader';

const { Option } = Select;

const SpecificSlotSelection = ({
    selectedPrvdr,
    onSlotSelected,
    onDataModified,
    onCancel,
    hideButtons = false,
    submitUrl,
    dataProto,
    orgSettingsData,
    vertical_id,
}) => {
    const [availableDays, setAvailableDays] = useState([]);
    const [selectedDay, setSelectedDay] = useState(null);
    const [availableSlots, setAvailableSlots] = useState([]);
    const [selectedSlot, setSelectedSlot] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [showBulkUploader, setShowBulkUploader] = useState(false);

    useEffect(() => {
        generateAvailableDays();
    }, []);

    useEffect(() => {
        if (selectedDay) {
            fetchAvailableSlots(selectedDay);
        }
    }, [selectedDay]);

    const generateAvailableDays = () => {
        const days = [];
        const currentDate = moment();

        // Generate next 14 days starting from today
        for (let i = 0; i < 14; i++) {
            const day = currentDate.clone().add(i, 'days');
            days.push({
                date: day.format('YYYY-MM-DD'),
                label: day.format('dddd, MMMM Do YYYY'),
                dayName: day.format('dddd'),
                displayDate: day.format('Do MMM'),
            });
        }

        setAvailableDays(days);
    };

    const fetchAvailableSlots = async (dayData) => {
        setLoading(true);
        setError(null);
        setAvailableSlots([]);

        try {
            const params = {
                org_id: selectedPrvdr,
                vertical_id: vertical_id,
                date: dayData.date,
            };

            const onComplete = (resp) => {
                console.log('Available slots response:', resp);
                if (resp.data && resp.data) {
                    setAvailableSlots(resp.data);
                } else {
                    setAvailableSlots([]);
                }
                setLoading(false);
            };

            const onError = (error) => {
                console.error('Error fetching available slots:', error);
                setError(http_utils.decodeErrorToMessage(error));
                setAvailableSlots([]);
                setLoading(false);
            };

            http_utils.performGetCall(
                '/booking/time-slots',
                params,
                onComplete,
                onError
            );
        } catch (err) {
            setError('Failed to fetch available slots');
            setAvailableSlots([]);
            setLoading(false);
        }
    };

    const handleDaySelection = (dayIndex) => {
        const selectedDayData = availableDays[dayIndex];
        setSelectedDay(selectedDayData);
        //  fetchAvailableSlots(selectedDayData);
        //  setSelectedSlot(null); // Reset slot selection when day changes
    };

    const handleSlotSelection = (slot) => {
        setSelectedSlot(slot);

        // If hideButtons is true, immediately call parent with selection
        setShowBulkUploader(true);
    };

    const getEnhancedDataProto = () => {
        console.log('dataProto', dataProto);
        // Add booking data to the proto if available
        const enhancedProto = [...dataProto];

        enhancedProto.bookingMode = 'select_specific_slot';
        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }
        if (selectedDay) {
            enhancedProto.selectedDay = selectedDay;
        }
        if (selectedSlot) {
            enhancedProto.selectedSlot = selectedSlot;
        }

        console.log('dataProtoenhancedProto', enhancedProto);

        return enhancedProto;
    };

    return (
        <div>
            {/* <Row gutter={16}>
                <Col span={24}>
                    <div style={{ marginBottom: 20 }}>
                        <h3>Select Specific Day and Time Slot</h3>
                        <p>
                            Choose the specific day and time slot for your
                            service requests:
                        </p>
                    </div>
                </Col>
            </Row> */}

            <Row gutter={16}>
                <Col span={12}>
                    <div style={{ marginBottom: 20 }}>
                        <label>Select Day:</label>
                        <Select
                            style={{ width: '100%', marginTop: 8 }}
                            placeholder="Choose a day"
                            onChange={handleDaySelection}
                            value={
                                selectedDay
                                    ? availableDays.findIndex(
                                          (d) => d.date === selectedDay.date
                                      )
                                    : undefined
                            }
                        >
                            {availableDays.map((day, index) => (
                                <Option key={index} value={index}>
                                    {day.label}
                                </Option>
                            ))}
                        </Select>
                    </div>
                </Col>
                {selectedDay && (
                    <Col span={12}>
                        <div style={{ marginBottom: 20 }}>
                            <label>Select Slot:</label>
                            <Select
                                style={{ width: '100%', marginTop: 8 }}
                                placeholder="Choose a day"
                                onChange={handleSlotSelection}
                                value={
                                    selectedSlot
                                        ? selectedSlot.label
                                        : undefined
                                }
                            >
                                {availableSlots.map((slot, index) => (
                                    <Option key={index} value={slot.db_id}>
                                        {slot.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                    </Col>
                )}

                {/* <Col span={12}>
                    {selectedDay && (
                        <div style={{ marginBottom: 20 }}>
                            <label>Available Time Slots:</label>
                            {loading ? (
                                <div
                                    style={{
                                        textAlign: 'center',
                                        marginTop: 20,
                                    }}
                                >
                                    <Spin />
                                    <p>Loading available slots...</p>
                                </div>
                            ) : availableSlots.length > 0 ? (
                                <div
                                    style={{
                                        marginTop: 8,
                                        maxHeight: 200,
                                        overflowY: 'auto',
                                    }}
                                >
                                    {availableSlots.map((slot, index) => (
                                        <Tag
                                            key={index}
                                            color={
                                                selectedSlot &&
                                                selectedSlot.label ===
                                                    slot.label
                                                    ? 'blue'
                                                    : 'default'
                                            }
                                            style={{
                                                margin: 4,
                                                cursor: 'pointer',
                                                padding: '4px 8px',
                                            }}
                                            onClick={() =>
                                                handleSlotSelection(slot)
                                            }
                                        >
                                            {slot.label}
                                        </Tag>
                                    ))}
                                </div>
                            ) : (
                                <div style={{ marginTop: 8, color: '#999' }}>
                                    No slots available for selected day
                                </div>
                            )}
                        </div>
                    )}
                </Col> */}
            </Row>

            {/* {selectedDay && selectedSlot && (
                <Row gutter={16}>
                    <Col span={24}>
                        <Card
                            style={{
                                marginBottom: 20,
                                backgroundColor: '#f6f6f6',
                            }}
                        >
                            <h4>Selected Booking Details:</h4>
                            <p>
                                <strong>Day:</strong> {selectedDay.label}
                            </p>
                            <p>
                                <strong>Time Slot:</strong> {selectedSlot.label}
                            </p>
                            {selectedSlot.available_qty && (
                                <p>
                                    <strong>Available Capacity:</strong>{' '}
                                    {selectedSlot.available_qty}
                                </p>
                            )}
                        </Card>
                    </Col>
                </Row>
            )} */}

            {error && (
                <Row gutter={16}>
                    <Col span={24}>
                        <div style={{ color: 'red', marginBottom: 20 }}>
                            Error: {error}
                        </div>
                    </Col>
                </Row>
            )}

            {/* Render Bulk Uploader after selection */}
            {showBulkUploader && (
                <Row gutter={16}>
                    <Col span={24}>
                        <div style={{ marginTop: 20 }}>
                            <BulkUploader
                                onDataModified={(entry_ids) => {
                                    setShowBulkUploader(false);
                                    if (onDataModified) {
                                        onDataModified(entry_ids);
                                    }
                                }}
                                submitUrl={submitUrl}
                                dataProto={getEnhancedDataProto()}
                                orgSettingsData={orgSettingsData}
                            />
                        </div>
                    </Col>
                </Row>
            )}
        </div>
    );
};

export default SpecificSlotSelection;
