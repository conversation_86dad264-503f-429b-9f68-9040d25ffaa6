import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Row, Col, message } from 'antd';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import MacroBookingComponent from './MacroBookingComponent';
import SpecificSlotSelection from './SpecificSlotSelection';

const { Option } = Select;

const BulkBookingModal = ({
    visible,
    onClose,
    selectedPrvdr,
    submitUrl,
    dataProto,
    orgSettingsData,
    onDataModified,
    vertical_id,
}) => {
    const [selectedBookingMode, setSelectedBookingMode] = useState(null);
    const [showBulkUploader, setShowBulkUploader] = useState(false);

    const bookingModeOptions = [
        { value: 'auto_book', label: 'Auto book available slots' },
        { value: 'select_fulfillment_day', label: 'Select fulfillment day' },
        { value: 'select_specific_slot', label: 'Select specific slot' },
    ];

    const handleBookingModeSelection = (mode) => {
        setSelectedBookingMode(mode);
    };

    const handleClose = () => {
        // Reset all states
        setSelectedBookingMode(null);
        setShowBulkUploader(false);
        onClose();
    };

    const getEnhancedDataProto = () => {
        console.log('dataProto', dataProto);
        // Add booking data to the proto if available
        const enhancedProto = [...dataProto];

        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }

        console.log('dataProtoenhancedProto', enhancedProto);

        return enhancedProto;
    };


    return (
        <Modal
            title="Bulk Creation with Booking Slots"
            visible={visible && !showBulkUploader}
            onCancel={handleClose}
            width={800}
        >
            <Row gutter={16}>
                <Col span={24}>
                    <div style={{ marginBottom: 20 }}>
                        <label>Select booking mode:</label>
                        <Select
                            style={{ width: '100%', marginTop: 8 }}
                            placeholder="Choose booking mode"
                            value={selectedBookingMode}
                            onChange={handleBookingModeSelection}
                        >
                            {bookingModeOptions.map((option) => (
                                <Option key={option.value} value={option.value}>
                                    {option.label}
                                </Option>
                            ))}
                        </Select>
                    </div>

                    {/* Show fields based on selected booking mode */}
                    {selectedBookingMode === 'select_fulfillment_day' && (
                        <div style={{ marginTop: 20 }}>
                            <MacroBookingComponent
                                selectedPrvdr={selectedPrvdr}
                                onDataModified={(entry_ids) => {
                                    handleClose();
                                    if (onDataModified) {
                                        onDataModified(entry_ids);
                                    }
                                }}
                                submitUrl={submitUrl}
                                dataProto={getEnhancedDataProto()}
                                orgSettingsData={orgSettingsData}
                                timeFormatMsg
                            />
                        </div>
                    )}

                    {selectedBookingMode === 'select_specific_slot' && (
                        <div style={{ marginTop: 20 }}>
                            <SpecificSlotSelection
                                selectedPrvdr={selectedPrvdr}
                                onDataModified={(entry_ids) => {
                                    handleClose();
                                    if (onDataModified) {
                                        onDataModified(entry_ids);
                                    }
                                }}
                                submitUrl={submitUrl}
                                dataProto={getEnhancedDataProto()}
                                orgSettingsData={orgSettingsData}
                                timeFormatMsg
                                vertical_id = {vertical_id}
                            />
                        </div>
                    )}

                    {selectedBookingMode === 'auto_book' && (
                        <div
                            style={{
                                marginTop: 20,
                                padding: 16,
                                backgroundColor: '#f6f6f6',
                                borderRadius: 6,
                            }}
                        >
                            <h4>Auto Book Mode</h4>
                            <BulkUploader
                                onDataModified={(entry_ids) => {
                                    handleClose();
                                    if (onDataModified) {
                                        onDataModified(entry_ids);
                                    }
                                }}
                                submitUrl={submitUrl}
                                dataProto={getEnhancedDataProto()}
                                orgSettingsData={orgSettingsData}
                                timeFormatMsg
                            />
                        </div>
                    )}
                </Col>
            </Row>
        </Modal>
    );
};

export default BulkBookingModal;
