import React, { useState, useEffect } from 'react';
import { Mo<PERSON>, <PERSON>, But<PERSON>, Row, Col, message } from 'antd';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import MacroBookingComponent from './MacroBookingComponent';
import SpecificSlotSelection from './SpecificSlotSelection';

const { Option } = Select;

const BulkBookingModal = ({
    visible,
    onClose,
    selectedPrvdr,
    submitUrl,
    dataProto,
    orgSettingsData,
    onDataModified,
}) => {
    const [selectedBookingMode, setSelectedBookingMode] = useState(null);
    const [showBulkUploader, setShowBulkUploader] = useState(false);
    const [showMacroBooking, setShowMacroBooking] = useState(false);
    const [showSpecificSlotSelection, setShowSpecificSlotSelection] =
        useState(false);
    const [selectedWeekData, setSelectedWeekData] = useState(null);
    const [selectedSlotData, setSelectedSlotData] = useState(null);

    const bookingModeOptions = [
        { value: 'auto_book', label: 'Auto book available slots' },
        { value: 'select_fulfillment_day', label: 'Select fulfillment day' },
        { value: 'select_specific_slot', label: 'Select specific slot' },
    ];

    const handleBookingModeSelection = (mode) => {
        setSelectedBookingMode(mode);

        switch (mode) {
            case 'auto_book':
                setShowBulkUploader(true);
                break;
            case 'select_fulfillment_day':
                setShowMacroBooking(true);
                break;
            case 'select_specific_slot':
                setShowSpecificSlotSelection(true);
                break;
            default:
                break;
        }
    };

    const handleMacroBookingComplete = (weekData) => {
        setSelectedWeekData(weekData);
        setShowMacroBooking(false);
        setShowBulkUploader(true);
    };

    const handleSpecificSlotComplete = (slotData) => {
        setSelectedSlotData(slotData);
        setShowSpecificSlotSelection(false);
        setShowBulkUploader(true);
    };

    const handleClose = () => {
        // Reset all states
        setSelectedBookingMode(null);
        setShowBulkUploader(false);
        setShowMacroBooking(false);
        setShowSpecificSlotSelection(false);
        setSelectedWeekData(null);
        setSelectedSlotData(null);
        onClose();
    };

    const getEnhancedDataProto = () => {
        console.log('dataProto', dataProto);
        // Add booking data to the proto if available
        const enhancedProto = [...dataProto];

        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }

        if (selectedBookingMode) {
            enhancedProto.bookingMode = selectedBookingMode;
        }

        if (selectedWeekData) {
            enhancedProto.selectedWeekData = selectedWeekData;
        }

        if (selectedSlotData) {
            enhancedProto.selectedSlotData = selectedSlotData;
        }
        console.log('dataProtoenhancedProto', enhancedProto);

        return enhancedProto;
    };

    return (
        <>
            {/* Booking Mode Selection Modal */}
            <Modal
                title="Choose Booking Mode"
                visible={
                    visible &&
                    !showBulkUploader &&
                    !showMacroBooking &&
                    !showSpecificSlotSelection
                }
                onCancel={handleClose}
                footer={null}
                width={500}
            >
                <Row gutter={16}>
                    <Col span={24}>
                        <div style={{ marginBottom: 16 }}>
                            <label>Select booking mode:</label>
                            <Select
                                style={{ width: '100%', marginTop: 8 }}
                                placeholder="Choose booking mode"
                                onChange={handleBookingModeSelection}
                            >
                                {bookingModeOptions.map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                    </Col>
                </Row>
            </Modal>

            {/* Macro Booking Component Modal */}
            <Modal
                title="Select Fulfillment Week"
                visible={showMacroBooking}
                onCancel={() => setShowMacroBooking(false)}
                footer={null}
                width={800}
            >
                <MacroBookingComponent
                    selectedPrvdr={selectedPrvdr}
                    onWeekSelected={handleMacroBookingComplete}
                    onCancel={() => setShowMacroBooking(false)}
                />
            </Modal>

            {/* Specific Slot Selection Modal */}
            <Modal
                title="Select Specific Time Slot"
                visible={showSpecificSlotSelection}
                onCancel={() => setShowSpecificSlotSelection(false)}
                footer={null}
                width={900}
            >
                <SpecificSlotSelection
                    selectedPrvdr={selectedPrvdr}
                    onSlotSelected={handleSpecificSlotComplete}
                    onCancel={() => setShowSpecificSlotSelection(false)}
                />
            </Modal>

            {/* Bulk Uploader Modal */}
            <Modal
                title="Bulk Upload Service Requests"
                visible={showBulkUploader}
                onCancel={handleClose}
                footer={null}
                width={1200}
            >
                <BulkUploader
                    onDataModified={(entry_ids) => {
                        handleClose();
                        if (onDataModified) {
                            onDataModified(entry_ids);
                        }
                    }}
                    submitUrl={submitUrl}
                    dataProto={getEnhancedDataProto()}
                    orgSettingsData={orgSettingsData}
                    timeFormatMsg
                />
            </Modal>
        </>
    );
};

export default BulkBookingModal;
