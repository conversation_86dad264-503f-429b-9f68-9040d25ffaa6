import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Row, Col, message, Card } from 'antd';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import MacroBookingComponent from './MacroBookingComponent';
import SpecificSlotSelection from './SpecificSlotSelection';

const { Option } = Select;

const BulkBookingModal = ({
    visible,
    onClose,
    selectedPrvdr,
    submitUrl,
    dataProto,
    orgSettingsData,
    onDataModified,
    vertical_id,
}) => {
    const [selectedBookingMode, setSelectedBookingMode] = useState(null);
    const [currentView, setCurrentView] = useState('mode_selection'); // 'mode_selection', 'day_selection', 'slot_selection', 'bulk_uploader'
    const [selectedWeekData, setSelectedWeekData] = useState(null);
    const [selectedSlotData, setSelectedSlotData] = useState(null);

    const bookingModeOptions = [
        {
            value: 'auto_book',
            title: 'Auto Book Available Slots',
            description:
                'System will automatically allocate best available slots',
        },
        {
            value: 'select_fulfillment_day',
            title: 'Select Fulfillment Day',
            description: 'Choose a specific day for fulfillment',
        },
        {
            value: 'select_specific_slot',
            title: 'Select Specific Slots',
            description: 'Choose exact time slots for delivery',
        },
    ];

    const handleBookingModeSelection = (mode) => {
        setSelectedBookingMode(mode);
        if (mode === 'auto_book') {
            setCurrentView('bulk_uploader');
        } else if (mode === 'select_fulfillment_day') {
            setCurrentView('day_selection');
        } else if (mode === 'select_specific_slot') {
            setCurrentView('slot_selection');
        }
    };

    const handleDaySelection = (dayData) => {
        setSelectedWeekData(dayData);
        setCurrentView('bulk_uploader');
    };

    const handleSlotSelection = (slotData) => {
        setSelectedSlotData(slotData);
        setCurrentView('bulk_uploader');
    };

    const handleClose = () => {
        // Reset all states
        setSelectedBookingMode(null);
        setCurrentView('mode_selection');
        setSelectedWeekData(null);
        setSelectedSlotData(null);
        onClose();
    };

    const renderModeSelection = () => {
        return (
            <div style={{ padding: '20px' }}>
                <h3 style={{ marginBottom: '20px', color: '#1890ff' }}>
                    📅 Choose Booking Mode
                </h3>
                <Row gutter={[16, 16]}>
                    {bookingModeOptions.map((option) => (
                        <Col span={8} key={option.value}>
                            <Card
                                hoverable
                                style={{
                                    textAlign: 'center',
                                    cursor: 'pointer',
                                    border:
                                        selectedBookingMode === option.value
                                            ? '2px solid #1890ff'
                                            : '1px solid #d9d9d9',
                                    borderRadius: '8px',
                                    height: '120px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    justifyContent: 'center',
                                }}
                                onClick={() =>
                                    handleBookingModeSelection(option.value)
                                }
                                bodyStyle={{ padding: '16px' }}
                            >
                                <h4
                                    style={{
                                        margin: '0 0 8px 0',
                                        color: '#1890ff',
                                    }}
                                >
                                    {option.title}
                                </h4>
                                <p
                                    style={{
                                        margin: 0,
                                        color: '#666',
                                        fontSize: '12px',
                                    }}
                                >
                                    {option.description}
                                </p>
                            </Card>
                        </Col>
                    ))}
                </Row>
            </div>
        );
    };

    const getEnhancedDataProto = () => {
        console.log('dataProto', dataProto);
        // Add booking data to the proto if available
        const enhancedProto = [...dataProto];

        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }

        console.log('dataProtoenhancedProto', enhancedProto);

        return enhancedProto;
    };

    return (
        <>
            {/* Mode Selection Modal */}
            <Modal
                title="Bulk Creation with Booking Slots"
                visible={visible && currentView === 'mode_selection'}
                onCancel={handleClose}
                width={800}
                footer={null}
            >
                {renderModeSelection()}
            </Modal>

            {/* Day Selection Modal */}
            <Modal
                title="Select Fulfillment Day"
                visible={visible && currentView === 'day_selection'}
                onCancel={handleClose}
                width={1000}
                footer={null}
            >
                <MacroBookingComponent
                    selectedPrvdr={selectedPrvdr}
                    onDaySelection={handleDaySelection}
                    showBulkUploader={false}
                />
            </Modal>

            {/* Slot Selection Modal */}
            <Modal
                title="Select Specific Time Slots"
                visible={visible && currentView === 'slot_selection'}
                onCancel={handleClose}
                width={1200}
                footer={null}
            >
                <SpecificSlotSelection
                    selectedPrvdr={selectedPrvdr}
                    onSlotSelection={handleSlotSelection}
                    showBulkUploader={false}
                    vertical_id={vertical_id}
                />
            </Modal>

            {/* Bulk Uploader Modal */}
            <Modal
                title="Bulk Upload"
                visible={visible && currentView === 'bulk_uploader'}
                onCancel={handleClose}
                width={1000}
                footer={null}
            >
                <BulkUploader
                    onDataModified={(entry_ids) => {
                        handleClose();
                        if (onDataModified) {
                            onDataModified(entry_ids);
                        }
                    }}
                    submitUrl={submitUrl}
                    dataProto={getEnhancedDataProto()}
                    orgSettingsData={orgSettingsData}
                    timeFormatMsg
                />
            </Modal>
        </>
    );
};

export default BulkBookingModal;
